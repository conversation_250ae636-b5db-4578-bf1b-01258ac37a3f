// THIS FILE IS AUTO GENERATED
import type { IconType } from '../lib/index'
export declare const TiAdjustBrightness: IconType;
export declare const TiAdjustContrast: IconType;
export declare const TiAnchorOutline: IconType;
export declare const TiAnchor: IconType;
export declare const TiArchive: IconType;
export declare const TiArrowBackOutline: IconType;
export declare const TiArrowBack: IconType;
export declare const TiArrowDownOutline: IconType;
export declare const TiArrowDownThick: IconType;
export declare const TiArrowDown: IconType;
export declare const TiArrowForwardOutline: IconType;
export declare const TiArrowForward: IconType;
export declare const TiArrowLeftOutline: IconType;
export declare const TiArrowLeftThick: IconType;
export declare const TiArrowLeft: IconType;
export declare const TiArrowLoopOutline: IconType;
export declare const TiArrowLoop: IconType;
export declare const TiArrowMaximiseOutline: IconType;
export declare const TiArrowMaximise: IconType;
export declare const TiArrowMinimiseOutline: IconType;
export declare const TiArrowMinimise: IconType;
export declare const TiArrowMoveOutline: IconType;
export declare const TiArrowMove: IconType;
export declare const TiArrowRepeatOutline: IconType;
export declare const TiArrowRepeat: IconType;
export declare const TiArrowRightOutline: IconType;
export declare const TiArrowRightThick: IconType;
export declare const TiArrowRight: IconType;
export declare const TiArrowShuffle: IconType;
export declare const TiArrowSortedDown: IconType;
export declare const TiArrowSortedUp: IconType;
export declare const TiArrowSyncOutline: IconType;
export declare const TiArrowSync: IconType;
export declare const TiArrowUnsorted: IconType;
export declare const TiArrowUpOutline: IconType;
export declare const TiArrowUpThick: IconType;
export declare const TiArrowUp: IconType;
export declare const TiAt: IconType;
export declare const TiAttachmentOutline: IconType;
export declare const TiAttachment: IconType;
export declare const TiBackspaceOutline: IconType;
export declare const TiBackspace: IconType;
export declare const TiBatteryCharge: IconType;
export declare const TiBatteryFull: IconType;
export declare const TiBatteryHigh: IconType;
export declare const TiBatteryLow: IconType;
export declare const TiBatteryMid: IconType;
export declare const TiBeaker: IconType;
export declare const TiBeer: IconType;
export declare const TiBell: IconType;
export declare const TiBook: IconType;
export declare const TiBookmark: IconType;
export declare const TiBriefcase: IconType;
export declare const TiBrush: IconType;
export declare const TiBusinessCard: IconType;
export declare const TiCalculator: IconType;
export declare const TiCalendarOutline: IconType;
export declare const TiCalendar: IconType;
export declare const TiCameraOutline: IconType;
export declare const TiCamera: IconType;
export declare const TiCancelOutline: IconType;
export declare const TiCancel: IconType;
export declare const TiChartAreaOutline: IconType;
export declare const TiChartArea: IconType;
export declare const TiChartBarOutline: IconType;
export declare const TiChartBar: IconType;
export declare const TiChartLineOutline: IconType;
export declare const TiChartLine: IconType;
export declare const TiChartPieOutline: IconType;
export declare const TiChartPie: IconType;
export declare const TiChevronLeftOutline: IconType;
export declare const TiChevronLeft: IconType;
export declare const TiChevronRightOutline: IconType;
export declare const TiChevronRight: IconType;
export declare const TiClipboard: IconType;
export declare const TiCloudStorageOutline: IconType;
export declare const TiCloudStorage: IconType;
export declare const TiCodeOutline: IconType;
export declare const TiCode: IconType;
export declare const TiCoffee: IconType;
export declare const TiCogOutline: IconType;
export declare const TiCog: IconType;
export declare const TiCompass: IconType;
export declare const TiContacts: IconType;
export declare const TiCreditCard: IconType;
export declare const TiCss3: IconType;
export declare const TiDatabase: IconType;
export declare const TiDeleteOutline: IconType;
export declare const TiDelete: IconType;
export declare const TiDeviceDesktop: IconType;
export declare const TiDeviceLaptop: IconType;
export declare const TiDevicePhone: IconType;
export declare const TiDeviceTablet: IconType;
export declare const TiDirections: IconType;
export declare const TiDivideOutline: IconType;
export declare const TiDivide: IconType;
export declare const TiDocumentAdd: IconType;
export declare const TiDocumentDelete: IconType;
export declare const TiDocumentText: IconType;
export declare const TiDocument: IconType;
export declare const TiDownloadOutline: IconType;
export declare const TiDownload: IconType;
export declare const TiDropbox: IconType;
export declare const TiEdit: IconType;
export declare const TiEjectOutline: IconType;
export declare const TiEject: IconType;
export declare const TiEqualsOutline: IconType;
export declare const TiEquals: IconType;
export declare const TiExportOutline: IconType;
export declare const TiExport: IconType;
export declare const TiEyeOutline: IconType;
export declare const TiEye: IconType;
export declare const TiFeather: IconType;
export declare const TiFilm: IconType;
export declare const TiFilter: IconType;
export declare const TiFlagOutline: IconType;
export declare const TiFlag: IconType;
export declare const TiFlashOutline: IconType;
export declare const TiFlash: IconType;
export declare const TiFlowChildren: IconType;
export declare const TiFlowMerge: IconType;
export declare const TiFlowParallel: IconType;
export declare const TiFlowSwitch: IconType;
export declare const TiFolderAdd: IconType;
export declare const TiFolderDelete: IconType;
export declare const TiFolderOpen: IconType;
export declare const TiFolder: IconType;
export declare const TiGift: IconType;
export declare const TiGlobeOutline: IconType;
export declare const TiGlobe: IconType;
export declare const TiGroupOutline: IconType;
export declare const TiGroup: IconType;
export declare const TiHeadphones: IconType;
export declare const TiHeartFullOutline: IconType;
export declare const TiHeartHalfOutline: IconType;
export declare const TiHeartOutline: IconType;
export declare const TiHeart: IconType;
export declare const TiHomeOutline: IconType;
export declare const TiHome: IconType;
export declare const TiHtml5: IconType;
export declare const TiImageOutline: IconType;
export declare const TiImage: IconType;
export declare const TiInfinityOutline: IconType;
export declare const TiInfinity: IconType;
export declare const TiInfoLargeOutline: IconType;
export declare const TiInfoLarge: IconType;
export declare const TiInfoOutline: IconType;
export declare const TiInfo: IconType;
export declare const TiInputCheckedOutline: IconType;
export declare const TiInputChecked: IconType;
export declare const TiKeyOutline: IconType;
export declare const TiKey: IconType;
export declare const TiKeyboard: IconType;
export declare const TiLeaf: IconType;
export declare const TiLightbulb: IconType;
export declare const TiLinkOutline: IconType;
export declare const TiLink: IconType;
export declare const TiLocationArrowOutline: IconType;
export declare const TiLocationArrow: IconType;
export declare const TiLocationOutline: IconType;
export declare const TiLocation: IconType;
export declare const TiLockClosedOutline: IconType;
export declare const TiLockClosed: IconType;
export declare const TiLockOpenOutline: IconType;
export declare const TiLockOpen: IconType;
export declare const TiMail: IconType;
export declare const TiMap: IconType;
export declare const TiMediaEjectOutline: IconType;
export declare const TiMediaEject: IconType;
export declare const TiMediaFastForwardOutline: IconType;
export declare const TiMediaFastForward: IconType;
export declare const TiMediaPauseOutline: IconType;
export declare const TiMediaPause: IconType;
export declare const TiMediaPlayOutline: IconType;
export declare const TiMediaPlayReverseOutline: IconType;
export declare const TiMediaPlayReverse: IconType;
export declare const TiMediaPlay: IconType;
export declare const TiMediaRecordOutline: IconType;
export declare const TiMediaRecord: IconType;
export declare const TiMediaRewindOutline: IconType;
export declare const TiMediaRewind: IconType;
export declare const TiMediaStopOutline: IconType;
export declare const TiMediaStop: IconType;
export declare const TiMessageTyping: IconType;
export declare const TiMessage: IconType;
export declare const TiMessages: IconType;
export declare const TiMicrophoneOutline: IconType;
export declare const TiMicrophone: IconType;
export declare const TiMinusOutline: IconType;
export declare const TiMinus: IconType;
export declare const TiMortarBoard: IconType;
export declare const TiNews: IconType;
export declare const TiNotesOutline: IconType;
export declare const TiNotes: IconType;
export declare const TiPen: IconType;
export declare const TiPencil: IconType;
export declare const TiPhoneOutline: IconType;
export declare const TiPhone: IconType;
export declare const TiPiOutline: IconType;
export declare const TiPi: IconType;
export declare const TiPinOutline: IconType;
export declare const TiPin: IconType;
export declare const TiPipette: IconType;
export declare const TiPlaneOutline: IconType;
export declare const TiPlane: IconType;
export declare const TiPlug: IconType;
export declare const TiPlusOutline: IconType;
export declare const TiPlus: IconType;
export declare const TiPointOfInterestOutline: IconType;
export declare const TiPointOfInterest: IconType;
export declare const TiPowerOutline: IconType;
export declare const TiPower: IconType;
export declare const TiPrinter: IconType;
export declare const TiPuzzleOutline: IconType;
export declare const TiPuzzle: IconType;
export declare const TiRadarOutline: IconType;
export declare const TiRadar: IconType;
export declare const TiRefreshOutline: IconType;
export declare const TiRefresh: IconType;
export declare const TiRssOutline: IconType;
export declare const TiRss: IconType;
export declare const TiScissorsOutline: IconType;
export declare const TiScissors: IconType;
export declare const TiShoppingBag: IconType;
export declare const TiShoppingCart: IconType;
export declare const TiSocialAtCircular: IconType;
export declare const TiSocialDribbbleCircular: IconType;
export declare const TiSocialDribbble: IconType;
export declare const TiSocialFacebookCircular: IconType;
export declare const TiSocialFacebook: IconType;
export declare const TiSocialFlickrCircular: IconType;
export declare const TiSocialFlickr: IconType;
export declare const TiSocialGithubCircular: IconType;
export declare const TiSocialGithub: IconType;
export declare const TiSocialGooglePlusCircular: IconType;
export declare const TiSocialGooglePlus: IconType;
export declare const TiSocialInstagramCircular: IconType;
export declare const TiSocialInstagram: IconType;
export declare const TiSocialLastFmCircular: IconType;
export declare const TiSocialLastFm: IconType;
export declare const TiSocialLinkedinCircular: IconType;
export declare const TiSocialLinkedin: IconType;
export declare const TiSocialPinterestCircular: IconType;
export declare const TiSocialPinterest: IconType;
export declare const TiSocialSkypeOutline: IconType;
export declare const TiSocialSkype: IconType;
export declare const TiSocialTumblerCircular: IconType;
export declare const TiSocialTumbler: IconType;
export declare const TiSocialTwitterCircular: IconType;
export declare const TiSocialTwitter: IconType;
export declare const TiSocialVimeoCircular: IconType;
export declare const TiSocialVimeo: IconType;
export declare const TiSocialYoutubeCircular: IconType;
export declare const TiSocialYoutube: IconType;
export declare const TiSortAlphabeticallyOutline: IconType;
export declare const TiSortAlphabetically: IconType;
export declare const TiSortNumericallyOutline: IconType;
export declare const TiSortNumerically: IconType;
export declare const TiSpannerOutline: IconType;
export declare const TiSpanner: IconType;
export declare const TiSpiral: IconType;
export declare const TiStarFullOutline: IconType;
export declare const TiStarHalfOutline: IconType;
export declare const TiStarHalf: IconType;
export declare const TiStarOutline: IconType;
export declare const TiStar: IconType;
export declare const TiStarburstOutline: IconType;
export declare const TiStarburst: IconType;
export declare const TiStopwatch: IconType;
export declare const TiSupport: IconType;
export declare const TiTabsOutline: IconType;
export declare const TiTag: IconType;
export declare const TiTags: IconType;
export declare const TiThLargeOutline: IconType;
export declare const TiThLarge: IconType;
export declare const TiThListOutline: IconType;
export declare const TiThList: IconType;
export declare const TiThMenuOutline: IconType;
export declare const TiThMenu: IconType;
export declare const TiThSmallOutline: IconType;
export declare const TiThSmall: IconType;
export declare const TiThermometer: IconType;
export declare const TiThumbsDown: IconType;
export declare const TiThumbsOk: IconType;
export declare const TiThumbsUp: IconType;
export declare const TiTickOutline: IconType;
export declare const TiTick: IconType;
export declare const TiTicket: IconType;
export declare const TiTime: IconType;
export declare const TiTimesOutline: IconType;
export declare const TiTimes: IconType;
export declare const TiTrash: IconType;
export declare const TiTree: IconType;
export declare const TiUploadOutline: IconType;
export declare const TiUpload: IconType;
export declare const TiUserAddOutline: IconType;
export declare const TiUserAdd: IconType;
export declare const TiUserDeleteOutline: IconType;
export declare const TiUserDelete: IconType;
export declare const TiUserOutline: IconType;
export declare const TiUser: IconType;
export declare const TiVendorAndroid: IconType;
export declare const TiVendorApple: IconType;
export declare const TiVendorMicrosoft: IconType;
export declare const TiVideoOutline: IconType;
export declare const TiVideo: IconType;
export declare const TiVolumeDown: IconType;
export declare const TiVolumeMute: IconType;
export declare const TiVolumeUp: IconType;
export declare const TiVolume: IconType;
export declare const TiWarningOutline: IconType;
export declare const TiWarning: IconType;
export declare const TiWatch: IconType;
export declare const TiWavesOutline: IconType;
export declare const TiWaves: IconType;
export declare const TiWeatherCloudy: IconType;
export declare const TiWeatherDownpour: IconType;
export declare const TiWeatherNight: IconType;
export declare const TiWeatherPartlySunny: IconType;
export declare const TiWeatherShower: IconType;
export declare const TiWeatherSnow: IconType;
export declare const TiWeatherStormy: IconType;
export declare const TiWeatherSunny: IconType;
export declare const TiWeatherWindyCloudy: IconType;
export declare const TiWeatherWindy: IconType;
export declare const TiWiFiOutline: IconType;
export declare const TiWiFi: IconType;
export declare const TiWine: IconType;
export declare const TiWorldOutline: IconType;
export declare const TiWorld: IconType;
export declare const TiZoomInOutline: IconType;
export declare const TiZoomIn: IconType;
export declare const TiZoomOutOutline: IconType;
export declare const TiZoomOut: IconType;
export declare const TiZoomOutline: IconType;
export declare const TiZoom: IconType;
